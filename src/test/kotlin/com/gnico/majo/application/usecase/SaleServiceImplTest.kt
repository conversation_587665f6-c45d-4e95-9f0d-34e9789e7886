package com.gnico.majo.application.usecase

import com.gnico.majo.adapter.controller.dto.SaleItemRequest
import com.gnico.majo.application.domain.model.*
import com.gnico.majo.application.port.out.*
import io.mockk.*
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.assertEquals

class SaleServiceImplTest {

    private lateinit var saleRepository: SaleRepositoryPort
    private lateinit var printer: PrinterPort
    private lateinit var salesReport: SalesReportPort
    private lateinit var usuarioRepository: UsuarioRepository
    private lateinit var clienteRepository: ClienteRepository
    private lateinit var tipoIvaRepository: TipoIvaRepository
    private lateinit var externalSaleRepository: ExternalSaleRepositoryPort
    private lateinit var afipOnlineService: AfipOnlineService
    private lateinit var afipOfflineService: AfipOfflineService
    private lateinit var saleService: SaleServiceImpl

    @BeforeEach
    fun setup() {
        saleRepository = mockk()
        printer = mockk()
        salesReport = mockk()
        usuarioRepository = mockk()
        clienteRepository = mockk()
        tipoIvaRepository = mockk()
        externalSaleRepository = mockk()
        afipOnlineService = mockk()
        afipOfflineService = mockk()

        saleService = SaleServiceImpl(
            saleRepository = saleRepository,
            printer = printer,
            salesReport = salesReport,
            usuarioRepository = usuarioRepository,
            clienteRepository = clienteRepository,
            tipoIvaRepository = tipoIvaRepository,
            externalSaleRepository = externalSaleRepository,
            afipOnlineService = afipOnlineService,
            afipOfflineService = afipOfflineService
        )
    }

    @Test
    fun `createSale with facturaOnline true should call AFIP online service`() = runBlocking {
        // Given
        val vendedor = "testuser"
        val usuario = Usuario("testuser", "Test User", "Test User", true)
        val saleId = Id(1)
        val sale = mockk<Sale>()
        val comprobanteId = Id(2)
        
        val itemRequest = SaleItemRequest(
            productoId = 1,
            productoNombre = "Test Product",
            cantidad = 1.0,
            precioUnitario = 100.0,
            subtotal = 100.0,
            unidadMedidaId = 1,
            tipoIvaId = 5
        )

        every { usuarioRepository.findByUsername(vendedor) } returns usuario
        every { tipoIvaRepository.findAll() } returns listOf(
            mockk { every { id } returns Id(5); every { porcentaje } returns BigDecimal("21.0") }
        )
        every { saleRepository.saveSale(any()) } returns saleId
        every { saleRepository.findSaleById(saleId) } returns sale
        coEvery { afipOnlineService.solicitarCAE(any(), any(), any()) } returns comprobanteId
        every { printer.printTicketFactura(any(), any()) } just Runs
        every { saleRepository.findComprobanteById(comprobanteId) } returns mockk()

        // When
        val result = saleService.createSale(
            clienteId = null,
            vendedor = vendedor,
            itemsRequest = listOf(itemRequest),
            medioPago = "EFECTIVO",
            imprimirTicket = true,
            facturaOnline = true,
            facturaOffline = false
        )

        // Then
        assertEquals(saleId, result)
        coVerify { afipOnlineService.solicitarCAE(sale, "FACTURA_B", 1) }
        verify { printer.printTicketFactura(any(), sale) }
        coVerify(exactly = 0) { afipOfflineService.crearComprobanteConCAEA(any(), any(), any()) }
    }

    @Test
    fun `createSale with facturaOffline true should call AFIP offline service`() = runBlocking {
        // Given
        val vendedor = "testuser"
        val usuario = Usuario("testuser", "Test User", "Test User", true)
        val saleId = Id(1)
        val sale = mockk<Sale>()
        val comprobanteId = Id(2)
        
        val itemRequest = SaleItemRequest(
            productoId = 1,
            productoNombre = "Test Product",
            cantidad = 1.0,
            precioUnitario = 100.0,
            subtotal = 100.0,
            unidadMedidaId = 1,
            tipoIvaId = 5
        )

        every { usuarioRepository.findByUsername(vendedor) } returns usuario
        every { tipoIvaRepository.findAll() } returns listOf(
            mockk { every { id } returns Id(5); every { porcentaje } returns BigDecimal("21.0") }
        )
        every { saleRepository.saveSale(any()) } returns saleId
        every { saleRepository.findSaleById(saleId) } returns sale
        coEvery { afipOfflineService.crearComprobanteConCAEA(any(), any(), any()) } returns comprobanteId
        every { printer.printTicketFactura(any(), any()) } just Runs
        every { saleRepository.findComprobanteById(comprobanteId) } returns mockk()

        // When
        val result = saleService.createSale(
            clienteId = null,
            vendedor = vendedor,
            itemsRequest = listOf(itemRequest),
            medioPago = "EFECTIVO",
            imprimirTicket = true,
            facturaOnline = false,
            facturaOffline = true
        )

        // Then
        assertEquals(saleId, result)
        coVerify { afipOfflineService.crearComprobanteConCAEA(sale, "FACTURA_B", 1) }
        verify { printer.printTicketFactura(any(), sale) }
        coVerify(exactly = 0) { afipOnlineService.solicitarCAE(any(), any(), any()) }
    }

    @Test
    fun `createSale with both facturaOnline and facturaOffline true should prioritize online`() = runBlocking {
        // Given
        val vendedor = "testuser"
        val usuario = Usuario("testuser", "Test User", "Test User", true)
        val saleId = Id(1)
        val sale = mockk<Sale>()
        val comprobanteId = Id(2)
        
        val itemRequest = SaleItemRequest(
            productoId = 1,
            productoNombre = "Test Product",
            cantidad = 1.0,
            precioUnitario = 100.0,
            subtotal = 100.0,
            unidadMedidaId = 1,
            tipoIvaId = 5
        )

        every { usuarioRepository.findByUsername(vendedor) } returns usuario
        every { tipoIvaRepository.findAll() } returns listOf(
            mockk { every { id } returns Id(5); every { porcentaje } returns BigDecimal("21.0") }
        )
        every { saleRepository.saveSale(any()) } returns saleId
        every { saleRepository.findSaleById(saleId) } returns sale
        coEvery { afipOnlineService.solicitarCAE(any(), any(), any()) } returns comprobanteId
        every { printer.printTicketFactura(any(), any()) } just Runs
        every { saleRepository.findComprobanteById(comprobanteId) } returns mockk()

        // When
        val result = saleService.createSale(
            clienteId = null,
            vendedor = vendedor,
            itemsRequest = listOf(itemRequest),
            medioPago = "EFECTIVO",
            imprimirTicket = true,
            facturaOnline = true,
            facturaOffline = true
        )

        // Then
        assertEquals(saleId, result)
        coVerify { afipOnlineService.solicitarCAE(sale, "FACTURA_B", 1) }
        verify { printer.printTicketFactura(any(), sale) }
        coVerify(exactly = 0) { afipOfflineService.crearComprobanteConCAEA(any(), any(), any()) }
    }

    @Test
    fun `createSale with no invoice but imprimirTicket true should print generic ticket`() = runBlocking {
        // Given
        val vendedor = "testuser"
        val usuario = Usuario("testuser", "Test User", "Test User", true)
        val saleId = Id(1)
        val sale = mockk<Sale>()
        
        val itemRequest = SaleItemRequest(
            productoId = 1,
            productoNombre = "Test Product",
            cantidad = 1.0,
            precioUnitario = 100.0,
            subtotal = 100.0,
            unidadMedidaId = 1,
            tipoIvaId = 5
        )

        every { usuarioRepository.findByUsername(vendedor) } returns usuario
        every { tipoIvaRepository.findAll() } returns listOf(
            mockk { every { id } returns Id(5); every { porcentaje } returns BigDecimal("21.0") }
        )
        every { saleRepository.saveSale(any()) } returns saleId
        every { saleRepository.findSaleById(saleId) } returns sale
        every { printer.printTicketGenerico(any()) } just Runs

        // When
        val result = saleService.createSale(
            clienteId = null,
            vendedor = vendedor,
            itemsRequest = listOf(itemRequest),
            medioPago = "EFECTIVO",
            imprimirTicket = true,
            facturaOnline = false,
            facturaOffline = false
        )

        // Then
        assertEquals(saleId, result)
        verify { printer.printTicketGenerico(sale) }
        coVerify(exactly = 0) { afipOnlineService.solicitarCAE(any(), any(), any()) }
        coVerify(exactly = 0) { afipOfflineService.crearComprobanteConCAEA(any(), any(), any()) }
    }
}
