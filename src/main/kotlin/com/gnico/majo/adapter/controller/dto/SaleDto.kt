package com.gnico.majo.adapter.controller.dto

import kotlinx.serialization.Serializable

@Serializable
data class SaleRequest(
    val vendedor: String, // username del usuario
    val clienteId: Int?,
    val medioPago: String = "EFECTIVO",
    val codigoTicketBalanza: String? = null,
    val idTicketBalanza: String? = null,
    val total: Double,
    val items: List<SaleItemRequest>,
    val imprimirTicket: Boolean = false,
    val facturaOnline: Boolean = false,
    val facturaOffline: Boolean = false
)

@Serializable
data class SaleItemRequest(
    val productoId: Int,
    val productoNombre: String,
    val cantidad: Double,
    val precioUnitario: Double,
    val subtotal: Double,
    val unidadMedidaId: Int,
    val tipoIvaId: Int = 5 // Tipo IVA por defecto (21%)
)

@Serializable
data class SaleResponse(val saleId: Int)

@Serializable
data class ErrorResponse(val message: String)

@Serializable
data class SaleDetailResponse(
    val invoiceId: String,
    val documentNo: String,
    val datePrinted: String?,
    val totalLines: Double,
    val grandTotal: Double,
    val createdBy: String,
    val lines: List<SaleLineDetailResponse>
)

@Serializable
data class SaleLineDetailResponse(
    val line: Int,
    val productId: Int,
    val productName: String,
    val qtyInvoiced: Double,
    val priceActual: Double,
    val lineNetAmt: Double,
    val uomId: Int
)
