package com.gnico.majo.adapter.controller.rest

import com.gnico.majo.adapter.controller.dto.SaleDetailResponse
import com.gnico.majo.adapter.controller.dto.SaleLineDetailResponse
import com.gnico.majo.adapter.controller.dto.SaleRequest
import com.gnico.majo.application.domain.model.ExternalSaleDetail
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.port.`in`.SaleService
import java.time.format.DateTimeFormatter

class SaleController(
    private val saleService: SaleService
) {
    suspend fun createSale(request: SaleRequest): Id {
        return saleService.createSale(
            clienteId = request.clienteId,
            vendedor = request.vendedor,
            itemsRequest = request.items,
            medioPago = request.medioPago,
            codigoTicketBalanza = request.codigoTicketBalanza,
            idTicketBalanza = request.idTicketBalanza,
            imprimirTicket = request.imprimirTicket,
            facturaOnline = request.facturaOnline,
            facturaOffline = request.facturaOffline
        )
    }

    suspend fun getSaleDetails(codigo: String): SaleDetailResponse? {
        val externalSaleDetail = saleService.getExternalSaleDetails(codigo)
        return externalSaleDetail?.let { mapToSaleDetailResponse(it) }
    }

    private fun mapToSaleDetailResponse(externalSale: ExternalSaleDetail): SaleDetailResponse {
        return SaleDetailResponse(
            invoiceId = externalSale.invoiceId,
            documentNo = externalSale.documentNo,
            datePrinted = externalSale.datePrinted?.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            totalLines = externalSale.totalLines.toDouble(),
            grandTotal = externalSale.grandTotal.toDouble(),
            createdBy = externalSale.createdBy,
            lines = externalSale.lines.map { line ->
                SaleLineDetailResponse(
                    line = line.line,
                    productId = line.productId,
                    productName = line.productName,
                    qtyInvoiced = line.qtyInvoiced.toDouble(),
                    priceActual = line.priceActual.toDouble(),
                    lineNetAmt = line.lineNetAmt.toDouble(),
                    uomId = line.uomId
                )
            }
        )
    }
}