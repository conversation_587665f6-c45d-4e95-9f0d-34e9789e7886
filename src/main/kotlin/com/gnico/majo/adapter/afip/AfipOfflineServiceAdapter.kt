package com.gnico.majo.adapter.afip

import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Comprobante
import com.gnico.majo.application.port.out.AfipOfflineService
import com.gnico.majo.application.port.out.SaleRepositoryPort

/**
 * Adaptador para el servicio de facturación offline con AFIP
 * Por ahora es una implementación stub que simula la funcionalidad
 */
class AfipOfflineServiceAdapter(
    private val saleRepository: SaleRepositoryPort
) : AfipOfflineService {
    
    override suspend fun crearComprobanteConCAEA(
        sale: Sale,
        tipoComprobante: String,
        puntoVenta: Int
    ): Id {
        println("*Creando comprobante offline con CAEA para venta ${sale.numeroVenta}*")
        println("*Tipo comprobante: $tipoComprobante, Punto venta: $puntoVenta*")
        
        // TODO: Implementar lógica real para usar CAEA
        // Por ahora simulamos la creación del comprobante con CAEA
        
        val comprobante = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = tipoComprobante,
            puntoVenta = puntoVenta,
            ventaId = sale.id ?: throw IllegalStateException("Sale debe tener ID")
        )
        
        // Simular CAEA utilizado
        val comprobanteConCAEA = comprobante.withCAE(
            cae = "21234567890123", // CAEA simulado
            estado = "AUTORIZADO_OFFLINE"
        )
        
        val comprobanteId = saleRepository.saveComprobante(comprobanteConCAEA)
        
        // Marcar la venta como comprobante emitido
        saleRepository.updateComprobanteEmitido(
            sale.id ?: throw IllegalStateException("Sale debe tener ID"), 
            true
        )
        
        println("*CAEA utilizado: ${comprobanteConCAEA.cae}*")
        
        return comprobanteId
    }
}
