package com.gnico.majo.adapter.afip

import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Comprobante
import com.gnico.majo.application.port.out.AfipOnlineService
import com.gnico.majo.application.port.out.SaleRepositoryPort

/**
 * Adaptador para el servicio de facturación online con AFIP
 * Por ahora es una implementación stub que simula la funcionalidad
 */
class AfipOnlineServiceAdapter(
    private val saleRepository: SaleRepositoryPort
) : AfipOnlineService {
    
    override suspend fun solicitarCAE(
        sale: Sale,
        tipoComprobante: String,
        puntoVenta: Int
    ): Id {
        println("*Solicitando CAE online para venta ${sale.numeroVenta}*")
        println("*Tipo comprobante: $tipoComprobante, Punto venta: $puntoVenta*")
        
        // TODO: Implementar llamada real a webservice AFIP
        // Por ahora simulamos la creación del comprobante con CAE
        
        val comprobante = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = tipoComprobante,
            puntoVenta = puntoVenta,
            ventaId = sale.id ?: throw IllegalStateException("Sale debe tener ID")
        )
        
        // Simular CAE obtenido de AFIP
        val comprobanteConCAE = comprobante.copy(
            cae = "68123456789012", // CAE simulado
            estado = "AUTORIZADO"
        )
        
        val comprobanteId = saleRepository.saveComprobante(comprobanteConCAE)
        
        // Marcar la venta como comprobante emitido
        saleRepository.updateComprobanteEmitido(
            sale.id ?: throw IllegalStateException("Sale debe tener ID"), 
            true
        )
        
        println("*CAE obtenido: ${comprobanteConCAE.cae}*")
        
        return comprobanteId
    }
}
