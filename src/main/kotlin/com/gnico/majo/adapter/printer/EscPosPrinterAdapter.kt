package com.gnico.majo.adapter.printer

import com.gnico.majo.application.domain.model.Comprobante
import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.port.out.PrinterPort

class EscPosPrinterAdapter : PrinterPort {
    override fun printComprobante(
        comprobante: Comprobante,
        sale: Sale
    ) {
        println("*prints comprobante*")
    }

    override fun printTicketGenerico(sale: Sale) {
        println("*prints ticket genérico para venta ${sale.numeroVenta}*")
    }

    override fun printTicketFactura(comprobante: Comprobante, sale: Sale) {
        println("*prints ticket factura para comprobante ${comprobante.numeroComprobante}*")
    }
}