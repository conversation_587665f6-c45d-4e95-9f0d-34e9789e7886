package com.gnico.majo.application.usecase

import com.gnico.majo.adapter.controller.dto.SaleItemRequest
import com.gnico.majo.application.port.`in`.SaleService
import com.gnico.majo.application.domain.model.Cliente
import com.gnico.majo.application.domain.model.Comprobante
import com.gnico.majo.application.domain.model.ExternalSaleDetail
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.domain.model.SaleItem
import com.gnico.majo.application.domain.model.Usuario
import com.gnico.majo.application.port.out.PrinterPort
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.SalesReportPort
import com.gnico.majo.application.port.out.SalesSummary
import com.gnico.majo.application.port.out.ClienteRepository
import com.gnico.majo.application.port.out.ExternalSaleRepositoryPort
import com.gnico.majo.application.port.out.TipoIvaRepository
import com.gnico.majo.application.port.out.UsuarioRepository
import com.gnico.majo.application.port.out.AfipOnlineService
import com.gnico.majo.application.port.out.AfipOfflineService
import java.math.BigDecimal
import java.time.LocalDateTime

class SaleServiceImpl(
    private val saleRepository: SaleRepositoryPort,
    private val printer: PrinterPort,
    private val salesReport: SalesReportPort,
    private val usuarioRepository: UsuarioRepository,
    private val clienteRepository: ClienteRepository,
    private val tipoIvaRepository: TipoIvaRepository,
    private val externalSaleRepository: ExternalSaleRepositoryPort
) : SaleService {

    override suspend fun createSale(
        clienteId: Int?,
        vendedorId: Int,
        itemsRequest: List<SaleItemRequest>,
        medioPago: String,
        codigoTicketBalanza: String?,
        idTicketBalanza: String?
    ): Id {
        // Obtener usuario por username - convertimos vendedorId a string para buscar por username
        val usuario = usuarioRepository.findByUsername(vendedorId.toString())
            ?: throw IllegalArgumentException("Usuario con username '$vendedorId' no encontrado")

        // Obtener cliente (opcional)
        val cliente = clienteId?.let { id ->
            clienteRepository.findById(Id(id))
                ?: throw IllegalArgumentException("Cliente $id no encontrado")
        }

        // Obtener tipos de IVA
        val tiposIva = tipoIvaRepository.findAll().associate { it.id to it.porcentaje }

        // Mapear ítems usando el factory method del dominio
        val items = itemsRequest.map { item ->
            val porcentajeIva = tiposIva[Id(item.tipoIvaId)]
                ?: throw IllegalArgumentException("Tipo IVA ${item.tipoIvaId} no encontrado")

            SaleItem.create(
                productoCodigo = item.productoId,
                cantidad = BigDecimal.valueOf(item.cantidad),
                precioUnitario = BigDecimal.valueOf(item.precioUnitario),
                tipoIva = Id(item.tipoIvaId),
                porcentajeIva = porcentajeIva
            )
        }

        // Crear venta usando el factory method del dominio
        val sale = Sale.create(
            cliente = cliente,
            usuario = usuario,
            items = items,
            medioPago = medioPago,
            codigoTicketBalanza = codigoTicketBalanza,
            idTicketBalanza = idTicketBalanza
        )

        return saleRepository.saveSale(sale)
    }




    override fun createComprobante(
        ventaId: Id,
        tipoComprobante: String,
        puntoVenta: Int
    ): Id {
        val sale = saleRepository.findSaleById(ventaId)
            ?: throw IllegalArgumentException("Venta ${ventaId.value} no encontrada")

        // Usar el factory method del dominio que incluye las validaciones
        val comprobante = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = tipoComprobante,
            puntoVenta = puntoVenta,
            ventaId = ventaId
        )

        val comprobanteId = saleRepository.saveComprobante(comprobante)
        val savedComprobante = saleRepository.findComprobanteById(comprobanteId)
            ?: throw IllegalStateException("Comprobante ${comprobanteId.value} no encontrado")

        // Marcar la venta como comprobante emitido
        saleRepository.updateComprobanteEmitido(ventaId, true)

        // Imprimir comprobante
        printer.printComprobante(savedComprobante, sale)

        return comprobanteId
    }

    /**
     * Obtiene un resumen de ventas con/sin comprobante.
     */
    fun getSalesSummary(startDate: LocalDateTime, endDate: LocalDateTime): SalesSummary {
        return salesReport.getSalesSummary(startDate, endDate)
    }

    override suspend fun getExternalSaleDetails(codigo: String): ExternalSaleDetail? {
        require(codigo.isNotBlank()) { "El código no puede estar vacío" }
        return externalSaleRepository.findSaleDetailsByDocumentNo(codigo)
    }
}