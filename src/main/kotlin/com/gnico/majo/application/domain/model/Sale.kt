package com.gnico.majo.application.domain.model

import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class Id(val value: Int) // Identificador genérico para el dominio

data class Cliente(val id: Id, val nombre: String, val cuit: String?)

@ConsistentCopyVisibility
data class Sale private constructor(
    val id: Id? = null,
    val numeroVenta: String,
    val cliente: Cliente?,
    val usuario: Usuario,
    val fechaVenta: LocalDateTime,
    val montoTotal: BigDecimal,
    val comprobanteEmitido: Boolean,
    val medioPago: String,
    val codigoTicketBalanza: String? = null,
    val idTicketBalanza: String? = null,
    val items: List<SaleItem>
) {
    companion object {
        fun create(
            cliente: Cliente?,
            usuario: Usuario,
            items: List<SaleItem>,
            medioPago: String,
            codigoTicketBalanza: String? = null,
            idTicketBalanza: String? = null
        ): Sale {
            require(items.isNotEmpty()) { "Una venta debe tener al menos un item" }
            require(medioPago.isNotBlank()) { "El medio de pago no puede estar vacío" }

            // Validar que el medio de pago sea válido
            MedioPago.fromString(medioPago)
                ?: throw IllegalArgumentException("Medio de pago '$medioPago' no válido")

            val numeroVenta = generateSaleNumber()
            val montoTotal = calculateTotal(items)

            return Sale(
                numeroVenta = numeroVenta,
                cliente = cliente,
                usuario = usuario,
                fechaVenta = LocalDateTime.now(),
                montoTotal = montoTotal,
                comprobanteEmitido = false,
                medioPago = medioPago,
                codigoTicketBalanza = codigoTicketBalanza,
                idTicketBalanza = idTicketBalanza,
                items = items
            )
        }

        private fun generateSaleNumber(): String {
            return "V-${UUID.randomUUID().toString().substring(0, 8)}"
        }

        private fun calculateTotal(items: List<SaleItem>): BigDecimal {
            return items.sumOf { it.totalWithTax() }
        }

        // Factory method para crear desde datos persistidos (usado por repositorios)
        fun fromPersistence(
            id: Id?,
            numeroVenta: String,
            cliente: Cliente?,
            usuario: Usuario,
            fechaVenta: LocalDateTime,
            montoTotal: BigDecimal,
            comprobanteEmitido: Boolean,
            medioPago: String,
            codigoTicketBalanza: String?,
            idTicketBalanza: String?,
            items: List<SaleItem>
        ): Sale {
            return Sale(
                id = id,
                numeroVenta = numeroVenta,
                cliente = cliente,
                usuario = usuario,
                fechaVenta = fechaVenta,
                montoTotal = montoTotal,
                comprobanteEmitido = comprobanteEmitido,
                medioPago = medioPago,
                codigoTicketBalanza = codigoTicketBalanza,
                idTicketBalanza = idTicketBalanza,
                items = items
            )
        }
    }

    fun canCreateComprobante(tipoComprobante: String): Boolean {
        val tipo = TipoComprobante.fromString(tipoComprobante) ?: return false
        return if (tipo.requiereCliente) cliente != null else true
    }

    fun validateComprobanteCreation(tipoComprobante: String) {
        val tipo = TipoComprobante.fromString(tipoComprobante)
            ?: throw IllegalArgumentException("Tipo de comprobante '$tipoComprobante' no válido")

        if (tipo.requiereCliente && cliente == null) {
            throw IllegalArgumentException("${tipo.descripcion} requiere un cliente")
        }
    }

    fun calculateTaxAmounts(): TaxAmounts {
        val impNeto = items.sumOf { it.baseImp }
        val impIva = items.sumOf { it.importeIva }
        val impTotal = impNeto.add(impIva)

        return TaxAmounts(
            impNeto = impNeto,
            impIva = impIva,
            impTotal = impTotal,
            impTotConc = BigDecimal.ZERO,
            impTrib = BigDecimal.ZERO
        )
    }
}

@ConsistentCopyVisibility
data class SaleItem private constructor(
    val productoCodigo: Int,
    val cantidad: BigDecimal,
    val precioUnitario: BigDecimal,
    val tipoIva: Id,
    val subtotal: BigDecimal,
    val baseImp: BigDecimal,
    val importeIva: BigDecimal
) {
    companion object {
        fun create(
            productoCodigo: Int,
            cantidad: BigDecimal,
            precioUnitario: BigDecimal,
            tipoIva: Id,
            porcentajeIva: BigDecimal
        ): SaleItem {
            require(cantidad > BigDecimal.ZERO) { "La cantidad debe ser mayor a cero" }
            require(precioUnitario >= BigDecimal.ZERO) { "El precio unitario no puede ser negativo" }
            require(porcentajeIva >= BigDecimal.ZERO) { "El porcentaje de IVA no puede ser negativo" }

            val subtotal = cantidad.multiply(precioUnitario)
            val baseImp = subtotal
            val importeIva = baseImp.multiply(porcentajeIva.divide(BigDecimal("100")))

            return SaleItem(
                productoCodigo = productoCodigo,
                cantidad = cantidad,
                precioUnitario = precioUnitario,
                tipoIva = tipoIva,
                subtotal = subtotal,
                baseImp = baseImp,
                importeIva = importeIva
            )
        }

        /**
         * Crea un SaleItem cuando el precio unitario ya incluye IVA
         */
        fun createWithTaxIncluded(
            productoCodigo: Int,
            cantidad: BigDecimal,
            precioUnitarioConIva: BigDecimal,
            tipoIva: Id,
            porcentajeIva: BigDecimal
        ): SaleItem {
            require(cantidad > BigDecimal.ZERO) { "La cantidad debe ser mayor a cero" }
            require(precioUnitarioConIva >= BigDecimal.ZERO) { "El precio unitario no puede ser negativo" }
            require(porcentajeIva >= BigDecimal.ZERO) { "El porcentaje de IVA no puede ser negativo" }

            val subtotalConIva = cantidad.multiply(precioUnitarioConIva)

            // Calcular base imponible (precio sin IVA)
            val divisor = BigDecimal.ONE.add(porcentajeIva.divide(BigDecimal("100")))
            val baseImp = subtotalConIva.divide(divisor, 2, java.math.RoundingMode.HALF_UP)
            val importeIva = subtotalConIva.subtract(baseImp)

            return SaleItem(
                productoCodigo = productoCodigo,
                cantidad = cantidad,
                precioUnitario = precioUnitarioConIva,
                tipoIva = tipoIva,
                subtotal = subtotalConIva,
                baseImp = baseImp,
                importeIva = importeIva
            )
        }

        // Factory method para crear desde datos persistidos (usado por repositorios)
        fun fromPersistence(
            productoCodigo: Int,
            cantidad: BigDecimal,
            precioUnitario: BigDecimal,
            tipoIva: Id,
            subtotal: BigDecimal,
            baseImp: BigDecimal,
            importeIva: BigDecimal
        ): SaleItem {
            return SaleItem(
                productoCodigo = productoCodigo,
                cantidad = cantidad,
                precioUnitario = precioUnitario,
                tipoIva = tipoIva,
                subtotal = subtotal,
                baseImp = baseImp,
                importeIva = importeIva
            )
        }
    }

    fun totalWithTax(): BigDecimal = subtotal
}

@ConsistentCopyVisibility
data class Comprobante private constructor(
    val id: Id? = null,
    val venta: Id,
    val tipoComprobante: String,
    val puntoVenta: Int,
    val numeroComprobante: Int,
    val cae: String,
    val fechaEmision: LocalDateTime,
    val fechaVencimientoCae: LocalDate,
    val impTotal: BigDecimal,
    val impTotConc: BigDecimal,
    val impNeto: BigDecimal,
    val impIva: BigDecimal,
    val impTrib: BigDecimal,
    val monId: String,
    val monCotiz: BigDecimal,
    val estado: String
) {
    companion object {
        fun createFromSale(
            sale: Sale,
            tipoComprobante: String,
            puntoVenta: Int,
            ventaId: Id
        ): Comprobante {
            // Validar que se puede crear el comprobante
            sale.validateComprobanteCreation(tipoComprobante)

            // Validar que el tipo de comprobante sea válido
            TipoComprobante.fromString(tipoComprobante)
                ?: throw IllegalArgumentException("Tipo de comprobante '$tipoComprobante' no válido")

            val taxAmounts = sale.calculateTaxAmounts()

            return Comprobante(
                venta = ventaId,
                tipoComprobante = tipoComprobante,
                puntoVenta = puntoVenta,
                numeroComprobante = 0, // Será asignado por el adaptador (o WSFE)
                cae = "PENDING", // Será actualizado tras WSFE
                fechaEmision = LocalDateTime.now(),
                fechaVencimientoCae = LocalDate.now().plusDays(10),
                impTotal = taxAmounts.impTotal,
                impTotConc = taxAmounts.impTotConc,
                impNeto = taxAmounts.impNeto,
                impIva = taxAmounts.impIva,
                impTrib = taxAmounts.impTrib,
                monId = "PES",
                monCotiz = BigDecimal("1.0"),
                estado = "PENDIENTE"
            )
        }

        // Factory method para crear desde datos persistidos (usado por repositorios)
        fun fromPersistence(
            id: Id?,
            venta: Id,
            tipoComprobante: String,
            puntoVenta: Int,
            numeroComprobante: Int,
            cae: String,
            fechaEmision: LocalDateTime,
            fechaVencimientoCae: LocalDate,
            impTotal: BigDecimal,
            impTotConc: BigDecimal,
            impNeto: BigDecimal,
            impIva: BigDecimal,
            impTrib: BigDecimal,
            monId: String,
            monCotiz: BigDecimal,
            estado: String
        ): Comprobante {
            return Comprobante(
                id = id,
                venta = venta,
                tipoComprobante = tipoComprobante,
                puntoVenta = puntoVenta,
                numeroComprobante = numeroComprobante,
                cae = cae,
                fechaEmision = fechaEmision,
                fechaVencimientoCae = fechaVencimientoCae,
                impTotal = impTotal,
                impTotConc = impTotConc,
                impNeto = impNeto,
                impIva = impIva,
                impTrib = impTrib,
                monId = monId,
                monCotiz = monCotiz,
                estado = estado
            )
        }
    }

    /**
     * Actualiza el CAE y estado del comprobante
     */
    fun withCAE(cae: String, estado: String): Comprobante {
        return Comprobante(
            id = this.id,
            venta = this.venta,
            tipoComprobante = this.tipoComprobante,
            puntoVenta = this.puntoVenta,
            numeroComprobante = this.numeroComprobante,
            cae = cae,
            fechaEmision = this.fechaEmision,
            fechaVencimientoCae = this.fechaVencimientoCae,
            impTotal = this.impTotal,
            impTotConc = this.impTotConc,
            impNeto = this.impNeto,
            impIva = this.impIva,
            impTrib = this.impTrib,
            monId = this.monId,
            monCotiz = this.monCotiz,
            estado = estado
        )
    }
}